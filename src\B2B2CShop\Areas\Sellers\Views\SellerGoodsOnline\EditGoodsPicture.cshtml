@using B2B2CShop.Entity
@model Goods
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsOnline";
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellergoodsonline-page");
    var goodsCommon = GoodsCommon.FindById(Model.GoodsCommonId);
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
    var goodsSkulist = GoodsSKUDetail.FindAllByGoodsId(goodsCommon?.Id ?? 0);
}
@await Html.PartialAsync("_Left")
<link rel="stylesheet" href="/static/home/<USER>/common.css">
<link rel="stylesheet" href="/static/home/<USER>/seller.css">
<link rel="stylesheet" href="/public/static/plugins/js/jquery-ui/jquery-ui.min.css">
<script>
    var BASESITEROOT = "";
    var HOMESITEROOT = "/static/home";
    var BASESITEURL = "http://b2b2c.h.com/index.php";
    var HOMESITEURL = "http://b2b2c.h.com/index.php/home";
</script>
<script src="/static/plugins/jquery-2.1.4.min.js"></script>
<script src="/public/static/plugins/js/jquery-ui/jquery-ui.min.js"></script>
<script src="/public/static/plugins/js/jquery-ui/jquery.ui.datepicker-zh-CN.js"></script>
<script src="/static/plugins/common.js"></script>
<script src="/static/plugins/jquery.validate.min.js"></script>
<script src="/static/plugins/additional-methods.min.js"></script>
<script src="/static/plugins/layer/layer.js"></script>
<script src="/static/home/<USER>/member.js"></script>
<script src="/static/plugins/js/dialog/dialog.js" id="dialog_js" charset="utf-8"></script>
<script>
    jQuery.browser = {};
    (function() {
    jQuery.browser.msie = false;
    jQuery.browser.version = 0;
    if (navigator.userAgent.match(/MSIE ([0-9]+)./)) {
    jQuery.browser.msie = true;
    jQuery.browser.version = RegExp.$1;
    }
    }
    )();
</script>
<div class="seller_right">
    <div class="seller_items">
        <ul class="tabsrow">
            <li><a href="@Url.Action("EditGoodsDetail",new{id = Model.Id})">@T("商品详情")</a></li>
            <li><a href="@Url.Action("EditGoodsSku",new{goodsId = Model.Id})">@T("商品SKU")</a></li>
            <li class="current"><a href="">@T("商品图片")</a></li>
        </ul>
    </div>
    <div class="p20">
        <form method="post" id="goods_image" action="@Url.Action("EditGoodsPicture")">
            <input type="hidden" name="commonid" value="@Model.GoodsCommonId">
            <div class="dssc-form-goods-pic">
                <div class="container" style="width:790px">
                    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                        @if (localizationSettings.IsEnable)
                        {
                            <ul class="layui-tab-title">
                                <li data="" class="layui-this">标准 </li>
                                @foreach (var item in LanguageList)
                                {
                                    <li data="@item.Id" class="LId">11 @item.DisplayName</li>
                                }
                            </ul>
                        }
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                @{
                                    foreach (var item in goodsSkulist)
                                    {

                                        <div class="dssc-goodspic-list">
                                            <div class="title">
                                                <h3>@(item.SpecValueDetail(0))</h3>
                                            </div>
                                            <ul dstype="ul0-@(item.Id)">
                                                @{
                                                    var goodsImages = GoodsImages.FindAllBySkuId(item?.Id ?? 0);
                                                    for (int i = 0; i < 5; i++)
                                                    {
                                                        string dstypeStr = "file_0" + i;
                                                        if (i < goodsImages.Count)//看看有没有商品图片
                                                        {
                                                            GoodsImages gimage = goodsImages[i];
                                                            var bumPic = AlbumPic.FindByNameAndSId(gimage.ImageUrl, goodsCommon?.StoreId ?? 0);

                                                            <li class="dssc-goodspic-upload">
                                                                <div class="upload-thumb">
                                                                    <img src="@bumPic?.Cover" dstype="@dstypeStr">
                                                                    <input type="hidden" name="img[0][@i][name]" value="@bumPic?.Name" dstype="@dstypeStr">
                                                                    <input type="hidden" name="img[0][@i][Id]" value="@gimage.Id" dsId="@dstypeStr">
                                                                </div>
                                                                <div class="show-default @(gimage.IsDefault == 1 ? "selected" : "")" dstype="@dstypeStr">
                                                                    <p>
                                                                        <i class="iconfont">&#xe64d;</i>
                                                                        默认主图                <input type="hidden" name="img[0][@i][default]" value="1">
                                                                    </p>
                                                                    <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                                </div>
                                                                <div class="show-sort">
                                                                    排序：
                                                                    <input name="img[0][@i][sort]" type="text" class="text" value="0" size="1" maxlength="1">
                                                                </div>
                                                                <div class="dssc-upload-btn">
                                                                    <a href="javascript:void(0);">
                                                                        <span>
                                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                        </span>
                                                                        <p>
                                                                            <i class="iconfont">&#xe733;</i>
                                                                            上传
                                                                        </p>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                        }
                                                        else
                                                        {
                                                            <li class="dssc-goodspic-upload">
                                                                <div class="upload-thumb">
                                                                    <img src="/uploads/common/default_goods_image.jpg" dstype="@dstypeStr">
                                                                    <input type="hidden" name="img[0][@i][name]" value="" dstype="@dstypeStr">
                                                                </div>
                                                                <div class="show-default" dstype="@dstypeStr">
                                                                    <p>
                                                                        <i class="iconfont">&#xe64d;</i>
                                                                        默认主图                <input type="hidden" name="img[0][@i][default]" value="0">
                                                                    </p>
                                                                    <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                                </div>
                                                                <div class="show-sort">
                                                                    排序：
                                                                    <input name="img[0][@i][sort]" type="text" class="text" value="@i" size="1" maxlength="1">
                                                                </div>
                                                                <div class="dssc-upload-btn">
                                                                    <a href="javascript:void(0);">
                                                                        <span>
                                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                        </span>
                                                                        <p>
                                                                            <i class="iconfont">&#xe733;</i>
                                                                            上传
                                                                        </p>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                        }

                                                    }
                                                }
                                            </ul>
                                            <div class="dssc-select-album">
                                                <a class="dssc-btn" href="/Sellers/SellerGoodsAdd/GoodsAlbumPic?demo=2" dstype="select-0-@(item.Id)">
                                                    <i class="iconfont">&#xe72a;</i>
                                                    11从图片空间选择 @item.Id
                                                </a>
                                                <a href="javascript:void(0);" dstype="close_album" class="dssc-btn ml5" style="display: none;">
                                                    <i class=" iconfont">&#xe67a;</i>
                                                    关闭相册
                                                </a>
                                            </div>
                                            <div dstype="album-0-@(item.Id)"></div>
                                        </div>
                                    }
                                }
                                
                            </div>
                            @if (localizationSettings.IsEnable)
                            {
                                @foreach (var item in LanguageList)
                                {
                                    <div class="layui-tab-item">
                                        @foreach (var skuDetail in goodsSkulist)
                                        {
                                            <div class="dssc-goodspic-list">
                                            <div class="title">
                                                <h3>@(skuDetail.SpecValueDetail(0))</h3>
                                            </div>
                                            <ul dstype="ul0-@(item.Id)-@(skuDetail.Id)">
                                                @{
                                                    var langoodsImages = GoodsImagesLan.FindAllByGoodsCommonIdAndLId(goodsCommon?.Id ?? 0, item.Id);
                                                    for (int i = 0; i < 5; i++)
                                                    {
                                                        string dstypeStr = "@(item.Id)file_0" + i + "" + item.Id;
                                                        if (i < langoodsImages.Count)
                                                        {
                                                            GoodsImagesLan medellan = langoodsImages[i];
                                                            var goodsImageslan = AlbumPic.FindByNameAndSId(medellan.ImageUrl, goodsCommon?.StoreId ?? 0);
                                                            <li class="dssc-goodspic-upload">
                                                                <div class="upload-thumb">
                                                                    <img src="@goodsImageslan?.Cover" dstype="@dstypeStr">
                                                                    <input type="hidden" name="[@item.Id].img[0][@i][name]" value="@goodsImageslan?.Name" dstype="@dstypeStr">
                                                                    <input type="hidden" name="[@item.Id].img[0][@i][Id]" value="@medellan.Id" dstype="@dstypeStr">
                                                                </div>
                                                                <div class="show-default selected" dstype="@dstypeStr">
                                                                    <p>
                                                                        <i class="iconfont">&#xe64d;</i>
                                                                        默认主图                <input type="hidden" name="[@item.Id].img[0][@i][default]" value="1">
                                                                    </p>
                                                                    <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                                </div>
                                                                <div class="show-sort">
                                                                    排序：
                                                                    <input name="[@item.Id].img[0][@i][sort]" type="text" class="text" value="0" size="1" maxlength="1">
                                                                </div>
                                                                <div class="dssc-upload-btn">
                                                                    <a href="javascript:void(0);">
                                                                        <span>
                                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                        </span>
                                                                        <p>
                                                                            <i class="iconfont">&#xe733;</i>
                                                                            上传
                                                                        </p>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                        }
                                                        else
                                                        {
                                                            <li class="dssc-goodspic-upload">
                                                                <div class="upload-thumb">
                                                                    <img src="/uploads/common/default_goods_image.jpg" dstype="@dstypeStr">
                                                                    <input type="hidden" name="[@item.Id].img[0][@i][name]" value="" dstype="@dstypeStr">
                                                                </div>
                                                                <div class="show-default" dstype="@dstypeStr">
                                                                    <p>
                                                                        <i class="iconfont">&#xe64d;</i>
                                                                        默认主图 <input type="hidden" name="[@item.Id].img[0][@i][default]" value="0">
                                                                    </p>
                                                                    <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                                </div>
                                                                <div class="show-sort">
                                                                    排序：
                                                                    <input name="[@item.Id].img[0][@i][sort]" type="text" class="text" value="@i" size="1" maxlength="1">
                                                                </div>
                                                                <div class="dssc-upload-btn">
                                                                    <a href="javascript:void(0);">
                                                                        <span>
                                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                        </span>
                                                                        <p>
                                                                            <i class="iconfont">&#xe733;</i>
                                                                            上传
                                                                        </p>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                        }

                                                    }

                                                }
                                            </ul>
                                            <div class="dssc-select-album">
                                                <a class="dssc-btn" href="/Sellers/SellerGoodsAdd/GoodsAlbumPic?demo=2" dstype="select-0-@(item.Id)-@(skuDetail.Id)">
                                                    <i class="iconfont">&#xe72a;</i>
                                                    22从图片空间选择 语言@(item.Id) SKU@(skuDetail.Id)
                                                </a>
                                                <a href="javascript:void(0);" dstype="close_album" class="dssc-btn ml5" style="display: none;">
                                                    <i class=" iconfont">&#xe67a;</i>
                                                    关闭相册
                                                </a>
                                            </div>
                                            <div dstype="album-0-@(item.Id)-@(skuDetail.Id)"></div>
                                        </div>
                                        }
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="sidebar">
                    <div class="alert alert-info alert-block" id="uploadHelp">
                        <div class="faq-img"></div>
                        <h4>上传要求：</h4>
                        <ul>
                            <li>1. 请使用jpg\jpeg\png等格式、单张大小不超过1M的正方形图片。</li>
                            <li>2. 上传图片最大尺寸将被保留为1280像素。</li>
                            <li>3. 每种颜色最多可上传5张图片或从图片空间中选择已有的图片，上传后的图片也将被保存在店铺图片空间中以便其它使用。</li>
                            <li>4. 通过更改排序数字修改商品图片的排列显示顺序。</li>
                            <li>5. 图片质量要清晰，不能虚化，要保证亮度充足。</li>
                            <li>6. 操作完成后请点下一步，否则无法在网站生效。</li>
                        </ul>
                        <h4>建议:</h4>
                        <ul>
                            <li>1. 主图为白色背景正面图。</li>
                            <li>2. 排序依次为正面图-&gt;背面图-&gt;侧面图-&gt;细节图。</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="bottom tc hr32">
                <input type="submit" class="submit" value="@T("保存商品图片")" />
            </div>
        </form>
    </div>
</div>
<script type="text/javascript" src="/static/plugins/ajaxfileupload.js" charset="utf-8"></script>
<script src="/static/plugins/jquery.ajaxContent.pack.js" type="text/javascript"></script>
<script src="/static/home/<USER>/sellergoods_add_step3.js"></script>
<script>
    var DEFAULT_GOODS_IMAGE = "/uploads/common/default_goods_image.jpg";

    $(function() {
        /* ajax打开图片空间 - 使用属性选择器为所有SKU项目绑定相册选择功能 */
        $('a[dstype^="select-0"]').each(function() {
            var $this = $(this);
            var dstype = $this.attr('dstype');
            var targetDstype = dstype.replace('select-0-', 'album-0-');

            $this.ajaxContent({
                event: 'click',
                loaderType: "img",
                loadingMsg: '/static/home/<USER>/loading.gif',
                target: 'div[dstype="' + targetDstype + '"]'
            }).click(function() {
                console.log('点击了相册选择按钮:', dstype, '目标:', targetDstype);
                currentActiveAlbum = targetDstype; // 记录当前活动的相册
                $(this).hide();
                $(this).next().show();
            });
        });
    });
</script>
<script src="/static/plugins/jquery.cookie.js"></script>
<script src="/static/home/<USER>/compare.js"></script>
<link rel="stylesheet" href="/static/plugins/perfect-scrollbar.min.css">
<script src="/static/plugins/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/plugins/js/qtip/jquery.qtip.min.js"></script>
<link href="/static/plugins/js/qtip/jquery.qtip.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/plugins/jquery.lazyload.min.js"></script>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script>
    var CreateImgs = "/Sellers/SellerGoodsAdd/UploadPicture";
</script>
<script asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;
    })

    var DEFAULT_GOODS_IMAGE = "/uploads/common/default_goods_image.jpg";
    var Querytheson = "@Url.Action("Querytheson", "ProductCategory")";
    var CreateImg = "@Url.Action("UploadImg")";
    var CreateImgs = "@Url.Action("UploadImg", "SpaceCategory", new { type = 1 })";

    var lid = "";
    var select = "";
    var del = "";
    var album = "";
    var currentActiveAlbum = ""; // 跟踪当前活动的相册
    $(function () {
        // 表单提交验证
        $('#goods_image').submit(function(e) {
            var hasDefault = false;
            $('.layui-tab-item.layui-show').find('.show-default').each(function() {
                if ($(this).find('input').val() == '1') {
                    hasDefault = true;
                    return false;
                }
            });
            
            if (!hasDefault) {
                layer.msg('请设置默认主图');
                return false;
            }
            return true;
        });
        // 标准版本的相册选择功能已在页面加载时绑定，这里不需要重复绑定

        $(".layui-tab-title").on("click", "li", function () {
            // 关闭所有打开的相册
            $('a[dstype^="select-"]').show();
            $('a[dstype="close_album"]').hide();
            $('div[dstype^="album-"]').empty();
            lid = $(this).attr("data");

            // 为当前语言版本绑定相册选择功能
            if (lid) {
                // 只绑定当前语言版本的选择器
                $('.layui-tab-item.layui-show a[dstype^="select-0"]').each(function() {
                    var $this = $(this);
                    var dstype = $this.attr('dstype');
                    var targetDstype = dstype.replace('select-0-', 'album-0-');

                    // 移除之前的绑定，重新绑定
                    $this.off('click').ajaxContent({
                        event: 'click',
                        loaderType: "img",
                        loadingMsg: '@(DHSetting.Current.CurDomainUrl)/images/loading.gif',
                        target: 'div[dstype="' + targetDstype + '"]'
                    }).click(function () {
                        console.log("语言版本相册被点击:", dstype, "目标:", targetDstype);
                        currentActiveAlbum = targetDstype; // 记录当前活动的相册
                        $(this).hide();
                        $(this).next().show();
                    });
                });
            }
        })

        // 重写关闭相册事件，清除当前活动相册记录
        $('a[dstype="close_album"]').off('click').click(function(){
            console.log('关闭相册，清除当前活动相册记录');
            currentActiveAlbum = ""; // 清除当前活动相册记录
            $(this).hide();
            $(this).prev().show();
            $(this).parent().next().html('');
        });

        $(".layui-tab-title li.layui-this").trigger("click");
        // 为所有图片项绑定删除事件
        $('.show-default').each(function() {
            selectDefaultImage($(this));
        });
    });
    // 选择默认主图&&删除
    function selectDefaultImage($this) {
        // 默认主题
        $this.click(function () {
            $(this).parents('ul:first').find('.show-default').removeClass('selected').find('input').val('0');
            $(this).addClass('selected').find('input').val('1');
        });
        // 删除
         // 删除处理
        $this.parents('li:first').find('a[dstype="del"]').click(function () {
            var $li = $(this).closest('li');
            var $thumb = $li.find('.upload-thumb');
            
            // 重置所有相关输入框的值
            $thumb.find('input[name*="name"]').val('');
            $thumb.find('img').attr('src', DEFAULT_GOODS_IMAGE);
            
            // 重置默认状态
            $this.removeClass('selected').find('input').val('0');
            
            // 重置排序值
            $li.find('input[name*="sort"]').val('0');
            
            return false;
        });
    }

    // 从图片空间插入主图
    function insert_img(name, src, color_id) {
        console.log('insert_img 被调用，当前活动相册:', currentActiveAlbum);

        if (!currentActiveAlbum) {
            console.log('没有记录当前活动的相册');
            return;
        }

        // 使用全局变量精确定位当前活动的相册
        var $currentAlbum = $('div[dstype="' + currentActiveAlbum + '"]');
        if ($currentAlbum.length === 0) {
            console.log('没有找到当前活动的相册元素:', currentActiveAlbum);
            return;
        }

        // 从相册div找到对应的ul
        var $albumContainer = $currentAlbum.closest('.dssc-goodspic-list');
        var $targetUl = $albumContainer.find('ul[dstype^="ul"]');

        if ($targetUl.length === 0) {
            console.log('没有找到目标ul元素');
            return;
        }

        console.log('找到目标ul:', $targetUl.attr('dstype'));
        var $_thumb = $targetUl.find('.upload-thumb');
        $_thumb.each(function () {
            if ($(this).find('input[name*="name"]').val() == '') {
                $(this).find('img').attr('src', src);
                $(this).find('input[name*="name"]').val(name);
                selectDefaultImage($(this).next());      // 选择默认主图
                return false;
            }
        });
    }

    function insert_imgs(id, name, src, color_id) {
        console.log('insert_imgs 被调用，当前活动相册:', currentActiveAlbum);

        if (!currentActiveAlbum) {
            console.log('没有记录当前活动的相册');
            return;
        }

        // 使用全局变量精确定位当前活动的相册
        var $currentAlbum = $('div[dstype="' + currentActiveAlbum + '"]');
        if ($currentAlbum.length === 0) {
            console.log('没有找到当前活动的相册元素:', currentActiveAlbum);
            return;
        }

        // 从相册div找到对应的ul
        var $albumContainer = $currentAlbum.closest('.dssc-goodspic-list');
        var $targetUl = $albumContainer.find('ul[dstype^="ul"]');

        if ($targetUl.length === 0) {
            console.log('没有找到目标ul元素');
            return;
        }

        console.log('找到目标ul:', $targetUl.attr('dstype'));
        var $_thumb = $targetUl.find('.upload-thumb');
        $_thumb.each(function () {
            if ($(this).find('input:eq(0)').val() == '') {
                $(this).find('img').attr('src', src);
                $(this).find('input:eq(0)').val(name);
                $(this).find('input:eq(1)').val(id);
                $(this).find('input:eq(2)').val(name);

                selectDefaultImage($(this).next());      // 选择默认主图
                return false;
            }
        });
    }
</script>

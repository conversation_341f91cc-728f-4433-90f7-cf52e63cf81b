﻿@using B2B2CShop.Entity
@inject IWorkContext workContext
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/modulesSearch.css");
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<style>
    .layui-laypage a,
    .layui-laypage span {
        font-size: 14px;
    }
</style>
<body>
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <a class="textSelect" href="@Url.Action("Index")">@T("产品分类")</a>
        </div>
        <!-- 正片开始 -->
        <div class="title">@Model.Entity.Name</div>
        <div class="filterBox">
            <!-- 搜索盒子 -->
            <div class="inputBox flex layui-form" style="padding: 1vw;">
                <input type="text" class="layui-input" style="margin-right: 20px;width: 270px;"
                placeholder="@T("请输入物料编号/关键字")">
                <!-- <input type="text" class="layui-input" style="margin-right: auto;" placeholder="所有制造商"> -->
                <select class="select" name="" id="">
                    <option value="1">@T("所有制造商")</option>
                </select>
                <button style="margin-right: auto;" class="button button_blue">@T("查询")</button>
            </div>
            <!-- 勾选盒子 -->
            <div class="checkBox_box flex" style="padding: .5vw 10px 20px 20px;">
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio1">
                    <label for="radio1" style="margin: -4px 5px;">@T("库存量")</label>
                </div>
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio2">
                    <label for="radio2" style="margin: -4px 5px;">@T("在售商品")</label>
                </div>
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio3">
                    <label for="radio3" style="margin: -4px 5px;">@T("符合RoSH")</label>
                </div>
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio4">
                    <label for="radio4" style="margin: -4px 5px;">@T("新产品")</label>
                </div>
            </div>

            <!-- 精确筛选盒子 -->
            <div class="precisionBox">
                <div style="width: 100%;margin: 10px 0px 0px 11px;">
                    <button class="button button_blue"
                    style="padding: .5vw 1.2vw;">
                        @T("重置所有")
                    </button>
                </div>
                <div class="container">
                    <div class="contentBox">
                        <div class="filterName">@T("输出功率")</div>
                        <div class="_content">
                            <div class="searchInputBox">
                                <div class="iconfont icon-search inputIcon"></div>
                                <input type="text" class="input search_input" placeholder="@T("搜索")">
                            </div>
                            <div class="paramterBox">
                                <div onclick="select(this)" data-type="0">1W</div>
                                <div onclick="select(this)" data-type="0" class="bgSelect">2W</div>
                                <div onclick="select(this)" data-type="0">3W</div>
                                <div onclick="select(this)" data-type="0">4W</div>
                                <div onclick="select(this)" data-type="0">5W</div>
                                <div onclick="select(this)" data-type="0">6W</div>
                                <div onclick="select(this)" data-type="0">3W</div>
                                <div onclick="select(this)" data-type="0">4W</div>
                                <div onclick="select(this)" data-type="0">5W</div>
                                <div onclick="select(this)" data-type="0">6W</div>
                            </div>
                        </div>
                    </div>
                    <div class="contentBox">
                        <div class="filterName">@T("输出电压")</div>
                        <div class="_content">
                            <div class="searchInputBox">
                                <div class="iconfont icon-search inputIcon"></div>
                                <input type="text" class="input search_input" placeholder="@T("搜索")">
                            </div>
                            <div class="paramterBox">
                                <div onclick="select(this)" data-type="1">1W</div>
                                <div onclick="select(this)" data-type="1" class="bgSelect">2W</div>
                                <div onclick="select(this)" data-type="1">3W</div>
                                <div onclick="select(this)" data-type="1">4W</div>
                                <div onclick="select(this)" data-type="1">5W</div>
                                <div onclick="select(this)" data-type="1">6W</div>
                                <div onclick="select(this)" data-type="1">3W</div>
                                <div onclick="select(this)" data-type="1">4W</div>
                                <div onclick="select(this)" data-type="1">5W</div>
                                <div onclick="select(this)" data-type="1">6W</div>
                            </div>
                        </div>
                        <div>
                            <button class="button button_blue" style="width: 100%;margin:.1vw 0px 0px 0px;
                            background-color: white;color: var(--blue-deep);">
                                @T("重置")
                            </button>
                        </div>
                    </div>
                    <div class="contentBox">
                        <div class="filterName">@T("输出电流")</div>
                        <div class="_content">
                            <div class="searchInputBox">
                                <div class="iconfont icon-search inputIcon"></div>
                                <input type="text" class="input search_input" placeholder="@T("搜索")">
                            </div>
                            <div class="paramterBox">
                                <div onclick="select(this)" data-type="2">1W</div>
                                <div onclick="select(this)" data-type="2" class="bgSelect">2W</div>
                                <div onclick="select(this)" data-type="2">3W</div>
                                <div onclick="select(this)" data-type="2">4W</div>
                                <div onclick="select(this)" data-type="2">5W</div>
                                <div onclick="select(this)" data-type="2">6W</div>
                                <div onclick="select(this)" data-type="2">3W</div>
                                <div onclick="select(this)" data-type="2">4W</div>
                                <div onclick="select(this)" data-type="2">5W</div>
                                <div onclick="select(this)" data-type="2">6W</div>
                            </div>
                        </div>
                    </div>
                    <div class="contentBox">
                        <div class="filterName">@T("工作温度")</div>
                        <div class="_content">
                            <div class="searchInputBox">
                                <div class="iconfont icon-search inputIcon"></div>
                                <input type="text" class="input search_input" placeholder="@T("搜索")">
                            </div>
                            <div class="paramterBox">
                                <div onclick="select(this)" data-type="3">1W</div>
                                <div onclick="select(this)" data-type="3" class="bgSelect">2W</div>
                                <div onclick="select(this)" data-type="3">3W</div>
                                <div onclick="select(this)" data-type="3">4W</div>
                                <div onclick="select(this)" data-type="3">5W</div>
                                <div onclick="select(this)" data-type="3">6W</div>
                                <div onclick="select(this)" data-type="3">3W</div>
                                <div onclick="select(this)" data-type="3">4W</div>
                                <div onclick="select(this)" data-type="3">5W</div>
                                <div onclick="select(this)" data-type="3">6W</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- table -->
                <div style="padding: 1vw 0px; background-color: white;">
                    <table class="layui-table" lay-skin="line" style="background-color: white;">
                        <colgroup>
                            <col width="150">
                            <col width="100">
                            <col width="170">
                            <col width="150">
                            <col width="200">
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>@T("型号/品牌/封装")</th>
                                <th>@T("库存")</th>
                                <th>@T("阶梯价格")</th>
                                <th>@T("货期")</th>
                                <th>@T("操作")</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.GoodsList)
                            {
                                <tr>
                                    <td>
                                        <div class="goodsInfo">
                                            <div class="flex"
                                            style="flex-direction: column;min-width: fit-content;">
                                                <img src="@item.GoodsImage" alt="商品图片"
                                                style="width: 100%;margin-top: auto;" onclick="toRouter(this)" data-link="@Url.Action("","")">
                                                @if (item.IsWish)
                                                {
                                                    <div style="margin-top: auto;cursor: pointer;" onclick="delWishlist('@item.SkuId')">
                                                        <i class="iconfont icon-aixin" style="color: var(--blue-deep)"></i>
                                                        @T("取消心愿清单")
                                                    </div>
                                                }
                                                else
                                                {

                                                    <div style="margin-top: auto;cursor: pointer;" onclick="addWishlist('@item.SkuId')">
                                                        <i class="iconfont icon-aixin2" style="color: var(--blue-deep)"></i>
                                                        @T("加入心愿清单")
                                                    </div>
                                                }

                                            </div>
                                            <div style="width: 60%;margin-left: 10%;">
                                                <p class="every"> <span class="name">@item.Name</span> </p>
                                                <p class="every">
                                                    <span class="name2">@T("分类"):</span>
                                                    <span class="name">@Model.Entity.Name</span>
                                                </p>
                                                <p class="every">               
                                                  <span class="name2"> @T("制造商"): </span>
                                                    <span class="name">Hi-Link/海凌科电子</span>
                                                </p>
                                                <p class="every">
                                                    <span class="name"> @T("封装"): </span>
                                                </p>
                                                <p class="every">
                                                    <span class="name">@T("描述"): </span>
                                                    <span class="name textOver">@item.AdvWord</span>
                                                </p>
                                                <div class="pointer" style="padding-top: 1vw;">
                                                    <i class="iconfont icon-pdf" style="color: var(--blue-deep)"></i>
                                                    @T("规格书")
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>@T("内陆仓"): 100k+ </div>
                                        <div> @T("海外仓"): 5806</div>
                                    </td>
                                    <td>
                                        <div class="textSelect">1000+: ¥10.12 </div>
                                        <div>2000+: ¥10.12 </div>
                                        <div>3000+: ¥10.12</div>
                                    </td>
                                    <td>
                                        <div class="flex layui-form" style="justify-content: left;">
                                            <div><input type="radio" checked> </div>
                                            <div style="margin-left: -20px;margin-top: 0.25vw;">@T("海外仓10-20个工作日")</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>@T("合计")：<span class="money" id="@item.Id">@symbolLeft 0</span> </div>
                                        <div class="layui-form-item">
                                            <div class="layui-input-group">
                                                <input type="number" class="layui-input @item.Id" style="max-width: 100px;" min="1" id="@(item.Id+"_"+item.GoodsPrice.ToString())">
                                                <div class="layui-input-split layui-input-suffix bgSelect"
                                                     style="cursor: pointer;" onclick="addCart('@item.SkuId')">
                                                    + @T("购物车")
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex" style="justify-content: left;">
                                            <div>@T("起订量"):1000</div>
                                            <div style="margin-left: 1vw;">@T("增 量"):1000</div>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
            <div id="pagingBox"></div>
        </div>
        <script>
            function select(dom) {
                const type = dom.getAttribute("data-type");
                const parentDom = dom.parentNode;
                $(parentDom).children().attr('class', '')
                // console.log(parentDom,$(parentDom));
                if (type == 0) {
                    dom.className = "bgSelect";
                } else if (type == 1) {
                    dom.className = "bgSelect";
                } else if (type == 2) {
                    dom.className = "bgSelect";
                } else if (type == 3) {
                    dom.className = "bgSelect";
                }
            }
            layui.use(function () {
                var laypage = layui.laypage;
                laypage.render({
                    elem: 'pagingBox',
                    count: @Model.Total, // 数据总数
                    theme: '#2C79E8',
                    prev: '@T("上一页")',
                    next: '@T("下一页")',
                    first: '@T("首页")',
                    last: '@T("尾页")',
                    countText: ['@T("共") ',' @T("条")'],
                    skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                    limitTemplet: function(item) {
                        return item + ' @T("条/页")';
                    },
                });
            });
        </script>
    </div>
</body>
<script asp-location="Footer">

    @* 加入心愿清单 *@
    function addWishlist(id)
    {
        $.post('@Url.Action("AddWishlist", "CubeHome")',{skuId:id},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                location.reload();
            }
        })
    }
    @* 取消心愿清单 *@
    function delWishlist(id)
    {
        $.post('@Url.Action("DelWishlist", "CubeHome")',{skuId:id},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                location.reload();
            }
        })
    }

    //加入购物车
    function addCart(SkuId)
    {

        var GoodsNum = parseInt($("."+SkuId).val());

        $.post("@Url.Action("AddCart", "CubeHome")",{SkuId,GoodsNum},function(res)
        {
            layui.layer.msg(res.msg);
            if (res.success) {
                // 如果添加成功，获取当前购物车数量
                if (typeof window.updateCartCount === 'function') {
                    window.updateCartCount(res.data);
                }
            }
        })
    }

    $("input[type='number']").on('input',function()
    {
        var quantity = parseInt($(this).val());

        var Ids = $(this).attr("id").split("_");

        var pricePerItem = parseFloat(Ids[1]);

         var totalAmount = quantity * pricePerItem;

         //console.log(11111,totalAmount);

        // var Id = $(this).attr("id");

        $("#"+Ids[0]).text('@symbolLeft'+totalAmount.toFixed(2));
    })

</script>
﻿@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IWorkContext workContext
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
    Goods goods = Model.Goods;
    PekHtml.AppendCssFileParts("~/public/css/pageCss/productDetails.css");
    Layout = "_RootNoHeader";
}
<style>


</style>
<div class="" style="margin-left:20px">
    <div class="price">
        <div>
            <input type="hidden" name="skuMarketPrice" id="skuMarketPrice" />
            <input type="hidden" name="skuPrice" id="skuPrice" />
            <input type="hidden" name="skuId" id="skuId" />
            <b id="currentPrice" style="font-size: 3.7vw;">@symbolLeft @goods.GoodsPrice</b>
            
        </div>
    </div>
    
    <div class="allStock">@T("库存总量")</div>
    <div class="tableBox" style="width: 320px; ">
        <table id="wareHouseTable" class="storeTable" style="width: 100%;text-align: center;">
            <thead>
                <tr style="background-color: var(--text-color4);">
                    <th style="padding: 0.2vw;">中国大陆</th>
                    <th style="padding: 0.2vw;">中国大陆</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th style="padding: 0.2vw;">1000</th>
                    <th style="padding: 0.2vw;">1000</th>
                </tr>
            </tbody>
        </table>
    </div>
    <div style="display: flex; align-items: center; margin-top:10px">
        <div>@T("数量")：</div>
        <div class="computer" style="margin-left: 8px;">
            <div class="count" id="count">@Model.Num</div>
        </div>
    </div>
    <div class="asideTextTitle totalPrice totalValue" id="totalPrice">@T("总价")：0.00</div>
    @foreach (var item in Model.dictSpec)
    {
        <div class="configBox" >
            <div class="model" style="width: 100%;" data-spec-id="@item.Key">
                @item.Value
            </div>
            @{
                var firstValue = true;
            }
            @foreach (GoodsSpecDto itemvalue in Model.specvalues)
            {
                if (itemvalue.Id != item.Key) continue;

                <div onclick="selectDiv(this)"
                     data-spec-id="@item.Key"
                     data-value-id="@itemvalue.VId"
                     data-select="@(firstValue ? "true" : "")">
                    @itemvalue.Value
                </div>
                firstValue = false;
            }
        </div>
    }
    <div style="position: fixed; left: 0; right: 0; bottom: 20px;">
        <button class="button asideBtn" style="color: var(--blue-deep);margin-left:20px;font-size: 2.7vw !important;" onclick="addCart()">@T("加入购物车")</button>
    </div>
</div>
<script asp-location="Footer">

    //加入购物车
    function addCart()
    {
        var GoodsId = "@Model.GoodsId";
        var GoodsNum = @Model.Num;
        var skuId = $("#skuId").val();
        $.post("@Url.Action("AddCart", "CubeHome")",{skuId,GoodsNum},function(res)
        {
            layui.layer.msg(res.msg);
            if (res.success) {
                parent.getChildrenData(res);//将结果返回给父窗体
                // 关闭弹窗
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index,res);
            }
        })
    }

    // 商品ID
    var goodsId = "@goods.Id";
    var originalPrice = "@goods.GoodsMarketPrice";

    // 更新总价函数
    function updateTotalPrice() {
        var priceElement = document.getElementById('totalPrice');
        var currentCount = @Model.Num;

        // 获取当前数量对应的单价
        var skuPrice = $('#skuPrice').val();
        unitPrice = parseFloat(skuPrice);
        originalPrice = parseFloat($('#skuMarketPrice').val()) || originalPrice;
        

        // 计算总价
        var totalPrice = currentCount * unitPrice;

        // 显示总价，保留两位小数
        priceElement.textContent = '@T("总价")：'+'@symbolLeft' + totalPrice.toFixed(2);

        // 更新单价显示
        var currentPriceElement = document.getElementById('currentPrice');
        currentPriceElement.textContent = '@symbolLeft' + unitPrice.toFixed(2);
        
    }

    // 页面加载完毕后，初始化事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化显示正确的总价
        updateTotalPrice();
    });
    
</script>
<script>
        // 存储选中的规格值
    let selectedSpecValues = {};

    // 初始化规格选择
    $(document).ready(function() {
        // 为规格选择添加点击事件
        $('.configBox div[data-value-id]').on('click', handleSpecSelect);

        // 触发默认选中的规格值
        $('.configBox div[data-select="true"]').each(function() {
            handleSpecSelect.call(this);
        });
    });

    // 处理规格选择
    function handleSpecSelect() {
        const $this = $(this);
        const specId = $this.data('spec-id');
        const valueId = $this.data('value-id');

        // 更新选中状态
        selectedSpecValues[specId] = valueId;

        // 检查是否所有规格都已选择
        const allSpecsSelected = $('.configBox').length === Object.keys(selectedSpecValues).length;

        if (allSpecsSelected) {
            // 获取所有选中的规格值ID
            const selectedIds = Object.values(selectedSpecValues);
            var currentCount = @Model.Num;
            // 调用后端获取价格
            $.post('@Url.Action("GetGoodsSkuDetail", "Goods")', {
                goodsId: '@goods.Id',
                specValueIds: selectedIds,
                number:currentCount
            }, function(result) {
                if (result.success) {
                    // 更新价格显示
                    $('#skuId').val(result.data.Id);
                    $('#skuMarketPrice').val(result.data.GoodsMarketPrice);
                    $('#skuPrice').val(result.data.GoodsPrice);
                    updateTotalPrice();
                     // 更新库存显示
                    if (result.extdata.wareHouseMaterials) {

                        const table = $('#wareHouseTable');
                        const thead = table.find('thead tr');
                        const tbody = table.find('tbody tr');
                        thead.empty();
                        tbody.empty();
                        if ( result.extdata.wareHouseMaterials.length > 0) {
                            // 更新表头和内容
                            result.extdata.wareHouseMaterials.forEach(warehouse => {
                                thead.append(`<th style="padding: 0.2vw;">${warehouse.WareHouseName}</th>`);
                                tbody.append(`<th style="padding: 0.2vw;">${warehouse.Quantity}</th>`);
                            });
                            // 启用按钮
                            $('.asideBtn').prop('disabled', false);
                            $('.asideBtn').css('opacity', '1');
                        } else {
                            thead.append(`<th style="padding: 0.2vw;">@T("暂无库存")</th>`);
                            // 禁用按钮
                            $('.asideBtn').prop('disabled', true);
                            $('.asideBtn').css('opacity', '0.5');
                        }
                    }
                }
            });
        }
    }

</script>
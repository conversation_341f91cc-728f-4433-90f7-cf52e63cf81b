﻿@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IWorkContext workContext
@inject IManageProvider _provider
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/cart.css");
    var user = _provider.TryLogin(Context);
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
    var exchangeRate = workContext.WorkingCurrency.ExchangeRate;
    // PekHtml.AppendScriptParts(ResourceLocation.Auto,"~/public/script/index.js");
    IList<Cart> cartlist = Model.cartList;
    var storelist = Model.storeList;
    var lId = Model.lId;
}
<script src=""></script>
<body>
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <a class="textSelect" href="@Url.Action("Index")">@T("购物车")</a>
        </div>
        <div class="checkOrderMain">
            <!-- 用户中心 -->
            <div class="content" data-show="true" style="display: block;padding: 0vw 1vw 1vw 1vw;">
                <!-- 所有订单-表格 -->
                <div class="tablesBox">
                    <table class="layui-table" lay-skin="line" style="background-color: white;margin: 0px;">
                        @{
                            int index = 0;
                            foreach (var storeItem in storelist)
                            {
                                <thead>
                                    <tr style="background-color: var(--text-color4);">
                                        <th>
                                            <div class="layui-form flex" style="margin-right: .5vw;place-items: center;justify-content: left;">
                                                <input type="checkbox" name="store" class="store-checkbox" lay-filter="store" data-store-id="@storeItem.Id">@storeItem.Name
                                                @* <i class="iconfont icon icon-zaixiankefu1" style="margin:0px 0 0 .5vw;" ></i> *@
                                            </div>
                                        </th>
                                        <th></th>
                                        <th class="tableIcon flex" style="margin-right: auto;justify-content: right;">
                                            <i class="iconfont icon icon-aixin2" style="margin-right: 1.5vw;"></i>
                                            <i class="iconfont icon icon-icdelete" onclick="delCartsByStore('@storeItem.Id')"></i>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <colgroup>
                                        <col width="60%">
                                        <col width="20%">
                                        <col width="5%">
                                    </colgroup>
                                    @foreach (var cartitem in cartlist)
                                    {
                                        if (cartitem.StoreId != storeItem.Id) continue;

                                        Goods goodsEntity = cartitem.Goods??new Goods();
                                        GoodsLan goodsLanEntity = GoodsLan.FindByGIdAndLId(goodsEntity.Id, lId);
                                        string goodsName = goodsLanEntity?.LanName ?? goodsEntity.Name;

                                        string goodsImage = GoodsImages.FindDefaultBySkuId(cartitem.SKUId, lId);
                                        if (goodsImage.IsNullOrEmpty())
                                        {
                                            goodsImage = GoodsLan.FindByGIdAndLId(cartitem.GoodsId, lId)?.LanGoodsImage ?? goodsEntity.GoodsImage ?? "";
                                            XTrace.WriteLine("商品图片不存在，使用默认图片：{0}", goodsImage);
                                        }

                                        var goodsImagePath = AlbumPic.FindByNameAndSId(goodsImage, cartitem.StoreId)?.Cover ?? "";
                                        List<WareHouseMaterialDto> wareHouses = WareHouseMaterial.FindAllByMaterialIdLan(cartitem.MaterialId, lId);

                                        var skuDetail = GoodsSKUDetail.FindById(cartitem.SKUId);
                                        var specvalue = skuDetail?.SpecValueDetail(lId);
                                        decimal goodsPrice = ((skuDetail?.GoodsPrice ?? goodsEntity.GoodsPrice) * exchangeRate).ToKeepTwoDecimal();
                                        if (cartitem.GoodsNum > 1 && skuDetail==null)
                                        {
                                            var materialTieredPrice = GoodsTieredPrice.GetPriceByGoodsIdAndSkuIdAndQuantity(cartitem.GoodsId, cartitem.SKUId ,cartitem.GoodsNum);
                                            if (materialTieredPrice != null)
                                            {
                                                goodsPrice = (materialTieredPrice.Price * exchangeRate).ToKeepTwoDecimal();
                                            }
                                        }
                                        goodsPrice = (cartitem.GoodsPrice * exchangeRate).ToKeepTwoDecimal();
                                        decimal goodsFreight = goodsEntity.GoodsFreight * exchangeRate;
                                        <tr>
                                            <td>
                                                <div class="goodsInfo" style="place-items: center;">
                                                    <div class="layui-form" style="margin-right: .5vw;">
                                                        <input type="checkbox" name="goods" class="goods-checkbox" lay-filter="goods" data-store-id="@storeItem.Id" data-goods-id="@cartitem.GoodsId" data-cart-id="@cartitem.Id" data-sku-id="@cartitem.SKUId">
                                                    </div>
                                                    <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                                        <img src="/public/images/icons/hlk.png" data-src="@goodsImagePath" class="lazy-load" alt="@T("商品图片")"
                                                             style="width: 100%;margin-top: auto;">
                                                    </div>
                                                    <div class="goodsInformation"
                                                         style="width: 70%;margin-left: 5%;margin-right: auto;">
                                                        <div class="tdTitle flex">
                                                            <div style="width: 80%;">
                                                                <p>@goodsName</p>
                                                                <p class="gray">@specvalue</p>
                                                            </div>

                                                            <a class="textSelect pointer" style="margin-left: auto;font-size: .7vw;"
                                                               href="@Url.Action("Index", "Goods", new { area = "", skuId = cartitem.SKUId })">
                                                                @T("商品详情")>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="fnTd">@*仓库物料库存*@
                                                @foreach (var item in wareHouses)
                                                {
                                                    <div>@item.WareHouseName:@item.Quantity</div>
                                                }
                                            </td>
                                            <td class="tableBtnBox">
                                                <!-- 计数器 -->
                                                <div class="computer" style="margin-left: auto;">
                                                    <div class="iconfont icon-jianhao" onclick="computeUpgradeCart(0,1,@index)"></div>
                                                    <div style="flex: 1;" class="count" id="count" data-id="@cartitem.Id">
                                                        <input type="text" name="goodsNum" id="goodsNum_@index" value="@cartitem.GoodsNum" class="layui-input" style="height:100%;width:50px; border:none;text-align:center">
                                                    </div>
                                                    <div class="iconfont icon-tianjia1" onclick="computeUpgradeCart(1,1,@index)"></div>
                                                </div>
                                                @*<div class="gray" id="freight" style="padding-top: 2px;width:150px;">@T("运费")：@symbolLeft @goodsFreight.ToString("F2")</div>*@
                                                <div class="gray" id="" style="padding-top: 2px;width:150px;">@T("起订量")：1</div>
                                                <div class="gray" id="price" style="padding-top: 2px;width:150px; display:none">@T("单价")：@symbolLeft @goodsPrice.ToString("F2")</div>
                                                <div class="gray" style="padding-top: 2px;width:150px; display:none">@T("总计")：@symbolLeft @((cartitem.GoodsNum * goodsPrice + goodsFreight).ToString("F2"))</div>
                                            </td>
                                        </tr>
                                        index++;
                                    }
                                </tbody>
                            }
                        }
                    </table>
                    @if (cartlist.Count > 0)
                    {
                        <div class="funcBox flex">
                            <div class="layui-form flex" style="border: none;">
                                <input type="checkbox" name="qx" lay-filter="qx" id="qx">
                                <label for="qx" class="pointer">@T("全选")</label>
                            </div>
                            <div style="margin-left: auto;" onclick="delSelectedCarts()">
                                <i class="iconfont icon-shanchu red1 iconSize"></i>
                                <span>@T("删除购物车")</span>
                            </div>
                        </div>
                    }
                </div>
                @if (cartlist.Count == 0)
                {
                    <div class="noData" data-show="false">
                        <img src="~/public/images/pics/konggouwuche.png" alt="" style="width: 30%;margin-top: 5%;">
                        <div class="noDataText">@T("您的购物车为空，去首页逛逛吧！")</div>
                        <button class="button button_blue noDataBtn" onclick="toRouter(this)" data-link="/">
                            <i class="iconfont icon-home" style="font-size: 1.2vw;"></i> @T("去首页")
                        </button>
                    </div>
                }
            </div>
            <!-- 侧边栏 -->
            <div style="display: block;" class="aside">
                <div class="asideItem serverInfoIcon" style="color: black;">
                    @T("结算明细")
                </div>
                @*<div class="asideItem" style="margin-top: 10px;">
                    <div>@T("商品总价")</div>
                    <div>¥ 0.00 </div>
                </div>
                <div class="asideItem">
                    <div>@T("运费价格")</div>
                    <div>¥ 0.00</div>
                </div>*@
                <div class="asideItem" style="font-size: 16px;">
                    <div style="color: black;">@T("合计")</div>
                    <div class="red serverInfoIcon"> <b>¥ </b> </div>
                </div>
                @*<form action="@Url.Action("CheckOrder", "ShoppingCart")" method="post" onsubmit="return validateForm()">
                    <div class="asideItem borderB" style="margin:10px 0 15px 0">
                        <div id="recordsContainer">
                            <!-- 动态生成的表单字段将插入这里 -->
                        </div>
                        <input type="hidden" name="cartIds" id="cartIds" value="">
                        <button class="button button_blue" style="width: 95%;margin: 10px auto;border-radius: 45px;">
                            @T("去结算")
                        </button>
                    </div>
                </form>*@
                <div class="asideItem borderB" style="margin:10px 0 15px 0">
                    <div id="recordsContainer">
                        <!-- 动态生成的表单字段将插入这里 -->
                    </div>
                    <input type="hidden" name="cartIds" id="cartIds" value="">
                    <button class="button button_blue" style="width: 95%;margin: 10px auto;border-radius: 45px;" onclick="submitOrder()">
                        @T("去结算")
                    </button>
                </div>
                <!--  -->
                <div class="serverInfo">
                    <div class="serverTitle" style="font-size: 15px;">@T("服务申明")</div>
                    <div class="serverTitle flex">
                        <div class="iconfont icon-sign serverInfoIcon" style="margin-left: .2vw;margin-right: .2vw;"></div>
                        <div>@T("快递")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div><div>@T("支持七天无理由退货")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div><div>@T("如果快递丢失，支持退货")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div><div>@T("如果快递损坏，支持退货")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div><div>@T("支持90天内免费换货")</div>
                    </div>

                    <div class="serverTitle"> <i class="iconfont icon-secured serverInfoIcon"></i> @T("安全与隐私")</div>
                    <div class="serverItem" style="margin-top: 3px;">
                        <div>@T("安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。")</div>
                        <div style="margin-top: 3px;">@T("安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。")</div>
                    </div>

                    <div class="serverTitle">
                        <i class="iconfont icon-money-circle serverInfoIcon"></i>
                        @T("支付安全")
                    </div>
                    <div class="serverItem" style="margin-top: 3px;">
                        <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                            <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                            <img src="../../images/icons/paypal.png" alt="">
                            <img src="../../images/icons/weixin.png" alt="">
                        </div>
                        <div>@T("与受欢迎的支付合作伙伴合作，您的个人信息是安全的。")</div>
                    </div>
                </div>
                <!-- 右侧边栏 -->
                <script>
                    /** 节流函数 */
                    // function throttle(func, delay) {
                    //     let timer = null;
                    //     return function(...args) {
                    //         if (timer === null) {
                    //         timer = setTimeout(() => {
                    //             func.apply(this, args);
                    //             timer = null;
                    //         }, delay);
                    //         }
                    //     };
                    // }
                    // function isAtFooter() {
                    //     const footerRect = document.querySelector('.footer').getBoundingClientRect();
                    //     const asideRect = document.querySelector('.aside').getBoundingClientRect();
                    //     return asideRect.bottom > footerRect.top && asideRect.top < footerRect.bottom;
                    // }
                    // function handleScroll() {
                    //     if (isAtFooter()) {
                    //         $('.aside').fadeOut(300);
                    //     }else{
                    //         $('.aside').fadeIn();
                    //     }
                    //     // 处理滚动事件的逻辑
                    // }

                    // // 节流处理滚动事件
                    // const throttledScroll = throttle(handleScroll, 200);
                    // window.addEventListener('scroll', throttledScroll);
                </script>
            </div>
        </div>
        <!-- 产品推荐 -国内才有 外国注重隐私 -->
        <div class="productComment">
            <div class="title">
                @T("相似推荐")
            </div>
            <div class="mainBox2_container">
                @foreach (GoodsDto item in ViewBag.Randomlist)
                {
                    <div class="mainBox2_content">
                        <div>
                            <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                                <img src="@item.GoodsImage" alt="@T("商品图片")">
                            </a>
                        </div>
                        <div class="mainBox2_content_desc">@item.Name</div>
                        <div class="gray">
                            @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                        </div>
                        <div class="mainBox2_content_price">@<EMAIL></div>
                        <!-- 加入购物车 -->
                        @if (item.GoodsStorage <= 0)
                        {
                            <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','@T("库存不足")',false)"></div>
                        }
                        else
                        {
                            <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','',true)"></div>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</body>
<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
window.addEventListener('load', function() {
    window.lazyLoadImages();
});
function submitOrder(){
    // 获取所有被选中的商品复选框
    var selectedIds = [];
    $('.goods-checkbox:checked').each(function () {
        var cartId = $(this).data('cart-id');
        if (cartId) {
            selectedIds.push(cartId);
        }
    });
    if (selectedIds.length === 0) {
        layer.msg('@T("请选择要结算的商品")');
        return;
    }
    // 逗号拼接
    var idsStr = selectedIds.join(',');

    // 清空所有选择项
    $('.goods-checkbox, .store-checkbox, #qx').prop('checked', false);
    if (typeof layui !== 'undefined' && layui.form) {
        layui.form.render('checkbox');
    }

    window.location.href = '@Url.Action("CheckOrder", "ShoppingCart")' + '?CIds=' + idsStr;
}



     calculateSelectedTotal();
      function computeUpgradeCart(type,addCount = 1,index = 0) {
        let $input = $('#goodsNum_'+index);
        let count = parseInt($input.val(), 10);
         if (typeof count !== 'number') {
             layer.msg('计算错误');
             return
         }
         if (count <= 1 && type == 0) return;
         type == 1?count += addCount:count -= addCount;
         // 更新输入框显示
         //$input.val(count);

         const cartId = $($('.count')[index]).data('id');
         UpgradePrice(index,count)
     }
     async function UpgradePrice(index,count){
         let $input = $('#goodsNum_'+index);
         const cartId = $($('.count')[index]).data('id');
         try {
             const res = await $.ajax({
                 url: '@Url.Action("UpgradeGoodsNum", "ShoppingCart")',
                 type: 'POST',
                 data: {
                     cartId: cartId,
                     num: count
                 }
             });

             if(res.success) {
                  // 更新输入框显示
                 $input.val(count);
                 // 使用res.data作为价格，而不是从DOM解析
                 const price = res.data;
                 // const freight = parseFloat($($('.tableBtnBox')[index]).find('.gray:eq(0)').text().split('@symbolLeft ')[1]);
                 const total = (count * price).toFixed(2);

                 // 更新单价显示
                 $($('.tableBtnBox')[index]).find('.gray:eq(1)').text(`@T("单价")：@symbolLeft ${price.toFixed(2)}`);
                 // 更新总价显示
                 $($('.tableBtnBox')[index]).find('.gray:eq(2)').text(`@T("总计")：@symbolLeft ${total}`);
                 calculateSelectedTotal();
             } else {
                 layer.msg(res.msg);
             }
         } catch (error) {
             layer.msg('@T("更新失败")');
         } finally {

         }
     }
    layui.use(['form'], function(){
         var form = layui.form;

         // 店铺选择
         form.on('checkbox(store)', function(data){
             const storeId = $(data.elem).data('store-id');
             const isChecked = data.elem.checked;
             // 设置该店铺下所有商品的选中状态
             $(`.goods-checkbox[data-store-id="${storeId}"]`).prop('checked', isChecked);
             form.render('checkbox'); // 重新渲染复选框
             updateAllCheckStatus();
             calculateSelectedTotal();
         });

         // 商品选择
         form.on('checkbox(goods)', function(data){
             const storeId = $(data.elem).data('store-id');
             const storeGoods = $(`.goods-checkbox[data-store-id="${storeId}"]`);
             const checkedGoods = storeGoods.filter(':checked');

             // 更新店铺复选框状态
             $(`.store-checkbox[data-store-id="${storeId}"]`).prop('checked',
                 storeGoods.length === checkedGoods.length);
             form.render('checkbox');
             updateAllCheckStatus();
             calculateSelectedTotal();
         });

         // 全选功能
         form.on('checkbox(qx)', function(data){
             const isChecked = data.elem.checked;
             $(`.store-checkbox, .goods-checkbox`).prop('checked', isChecked);
             form.render('checkbox');
             calculateSelectedTotal();
         });
         // 初始化表单
         form.render();
     });
      // 更新全选框状态
     function updateAllCheckStatus() {
         const allCheckboxes = $('.goods-checkbox');
         const checkedCheckboxes = $('.goods-checkbox:checked');
         $('#qx').prop('checked', allCheckboxes.length === checkedCheckboxes.length);
         form.render('checkbox');
     }

     // 计算选中商品的总价
     function calculateSelectedTotal() {
         let totalPrice = 0;
         // let totalFreight = 0;
         const selectedIds = [];
         const selectedcartIds = [];
         const selectedNums = [];
         const records = [];
          // 清空原有的记录容器
         $('#recordsContainer').empty();

         $('.goods-checkbox:checked').each(function() {
             const tr = $(this).closest('tr');
             const count = parseInt(tr.find('input[id^="goodsNum_"]').val(), 10);;
             const price = parseFloat(tr.find('#price').text().split('@symbolLeft')[1]);
             console.log('price',price)
             // const freight = parseFloat(tr.find('#freight').text().split('@symbolLeft')[1]);
             // 收集选中的商品ID
             selectedcartIds.push($(this).data('cart-id'));
             // 构建商品记录对象
             records.push({
                 goodsId: $(this).data('goods-id'),
                 skuId: $(this).data('sku-id'),
                 num: count
             });
             totalPrice += price * count;
             // totalFreight += freight;

         });


         // 更新侧边栏总价
          // $('.aside .asideItem:eq(1) div:eq(1)').text(`@symbolLeft ${totalPrice.toFixed(2)}`);
          // $('.aside .asideItem:eq(2) div:eq(1)').text(`@symbolLeft ${totalFreight.toFixed(2)}`);
          $('.aside .asideItem:eq(1) div:eq(1) b').text(`@symbolLeft ${totalPrice.toFixed(2)}`);
         // 更新隐藏input的值
          // 遍历选中的商品，生成表单字段
         $('.goods-checkbox:checked').each(function(index) {
             const tr = $(this).closest('tr');
             const count = parseInt(tr.find('.count').text());

             // 创建隐藏字段
             $('<input>').attr({
                 type: 'hidden',
                 name: `records[${index}].goodsId`,
                 value: $(this).data('goods-id')
             }).appendTo('#recordsContainer');

             $('<input>').attr({
                 type: 'hidden',
                 name: `records[${index}].skuId`,
                 value: $(this).data('sku-id') || ''
             }).appendTo('#recordsContainer');

             $('<input>').attr({
                 type: 'hidden',
                 name: `records[${index}].num`,
                 value: count
             }).appendTo('#recordsContainer');

             // ... 计算总价等其他逻辑保持不变 ...
         });
         $('#cartIds').val(selectedcartIds.join(','));
     }
     // 删除选中商品
     function delSelectedCarts() {
         const selectedGoods = $('.goods-checkbox:checked');
         if (selectedGoods.length === 0) {
             layer.msg('@T("请选择要删除的商品")');
             return;
         }

         // 确认删除
         layer.confirm('@T("确定要删除选中的商品吗？")', {
             title:'@T("提示")',
             btn: ['@T("确定")', '@T("取消")']
         }, function() {
             const ids = [];
             selectedGoods.each(function() {
                 ids.push($(this).data('cart-id'));
             });

             $.ajax({
                 url: '@Url.Action("DelCarts", "ShoppingCart")',
                 type: 'POST',
                 data: {
                     ids: ids.join(',')
                 },
                 success: function(res) {
                     if (res.success) {
                        layer.msg(res.msg);
                        if (typeof window.updateCartCount === 'function') 
                        {
                            window.updateCartCount(res.data);
                        }
                        location.reload();
                     } else {
                         layer.msg(res.msg);
                     }
                 },
                 error: function() {
                     layer.msg('@T("删除失败")');
                 }
             });
         });
     }
     // 删除店铺下所有购物车商品
     function delCartsByStore(storeId) {
         layer.confirm('@T("确定要删除该店铺下的所有商品吗？")', {
             btn: ['@T("确定")', '@T("取消")'],
             title:'@T("警告")'
         }, function() {
             $.ajax({
                 url: '@Url.Action("DelCartsByStoreId", "ShoppingCart")',
                 type: 'POST',
                 data: {
                     storeId: storeId
                 },
                 success: function(res) {
                     if (res.success) {
                        layer.msg(res.msg);
                        if (typeof window.updateCartCount === 'function')
                        {
                            window.updateCartCount(res.data);
                        }
                        location.reload();
                     } else {
                         layer.msg(res.msg);
                     }
                 },
                 error: function() {
                     layer.msg('@T("删除失败")');
                 }
             });
         });
     }
     function validateForm() {
          var u = "@user";
          if(u == null | u == '')
          {
             window.location.href = '/Login';
             return false;
          }
         const cartIds = $('#cartIds').val();
         if (!cartIds) {
             layer.msg('@T("请选择要结算的商品")');
             return false;
         }
         const selectedStores = $('.store-checkbox:checked').length;
         if (selectedStores > 1) {
             layer.msg('@T("只能选择一个店铺进行结算")');
             return false;
         }
             // 清空所有选择项
    $('.goods-checkbox, .store-checkbox, #qx').prop('checked', false);
    if (typeof layui !== 'undefined' && layui.form) {
        layui.form.render('checkbox');
    }
         return true;
     }
     $(document).on('input', 'input[id^="goodsNum_"]', function() {
        // 只保留数字，且最小为1
        this.value = this.value.replace(/[^0-9]/g, '');
        if (this.value === '' || parseInt(this.value) < 1) {
            this.value = 1;
        }
        // 获取下标
        var id = $(this).attr('id');
        var index = id.split('_')[1];
        // 获取当前数量
        var count = parseInt($(this).val(), 10);
        UpgradePrice(index,count)
    });
</script>
<style>
    .layui-input:focus,
    .layui-textarea:focus {
        border-color: none !important;
        box-shadow: none !important;
    }
</style>
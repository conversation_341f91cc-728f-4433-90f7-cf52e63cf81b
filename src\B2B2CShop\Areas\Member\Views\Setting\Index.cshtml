﻿@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IManageProvider _provider
@inject IWebHelper _webHelper
@inject IWorkContext workContext
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/accountSettings.css");

    var user = _provider.TryLogin(Context);
    var returnUrl = _webHelper.GetRawUrl(Context.Request);
    var site = SiteInfo.GetDefaultSeo();
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Setting")">@T("账户设置")</a>
    </div>
    <!-- <iframe src="../iframes/success.html" frameborder="0"  class="mask" id="mask"></iframe> -->
    <!-- 用户中心 -->
    <div class="userInfoBox">
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish",new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
@*             <a href="@Url.Action("Index", "Message",new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate",new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice",new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact",new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
            @*             <a href="@Url.Action("Index", "PaymentMethod",new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content">
            <div class="userInfo">
                @* <div class="flex"> <div class="label">@T("账号ID") : </div>  <div class="text"> @Model.ID </div>  </div> *@
                <div class="flex"> <div class="label">@T("账号名称") :</div> <div> <input class="input input2" value="@Model.Name" id="Name"> </div>  </div>
                <div class="flex">
                    <div class="label">@T("邮箱账号") :</div>

                    <div id="email" data-hidden="true">@Model.Mail</div>
                    <i class="iconfont icon-denglu-mimabukejian toggle-visibility" onclick="toggleEmailVisibility()" style="font-size: 23px;color: var(--text-color);cursor: pointer;"></i>
                    <a class="update" href="@Url.Action("EditMail")">&nbsp; @T("修改")</a>
                </div>
                <div class="flex">
                    <div class="label">@T("登录密码") :</div>
                    <div>  </div>&nbsp;<a class="update" href="@Url.Action("EditPwd")">@T("修改")</a>
                </div>
                <div class="flex">
                    <div class="label">@T("联系地址") :</div>
                    <div>
                        <input class="input input2" value="@ViewBag.Address" id="Address">
                    </div>
                </div>

                <div class="flex"> <div class="label"></div> <button class="button button_blue save" onclick="btn_submlit()">@T("保存")</button> </div>
            </div>
        </div>
    </div>
    <!-- 产品推荐 -国内才有 外国注重隐私 -->
    <div class="productComment">
        <div class="title">
            @T("相似推荐")
        </div>
        <div class="mainBox2_container">
            @foreach (GoodsDto item in ViewBag.Randomlist)
            {
                <div class="mainBox2_content">
                    <div>
                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                            <img src="@item.GoodsImage" alt="@T("商品图片")">
                        </a>
                    </div>
                    <div class="mainBox2_content_desc">@item.Name</div>
                    <div class="gray">
                        @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                    </div>
                    <div class="mainBox2_content_price">@<EMAIL></div>
                    <!-- 加入购物车 -->
                    @if (item.GoodsStorage <= 0)
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','@T("库存不足")',false)"></div>
                    }
                    else
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','',true)"></div>
                    }
                </div>
            }
        </div>
    </div>
</div>
<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
window.addEventListener('load', function() {
    window.lazyLoadImages();
});
   function btn_submlit(){
       var Name = $("#Name").val();
       var Address = $("#Address").val();
       $.post('@Url.Action("Edit")',{Name,Address},function(res)
       {
            layui.layer.msg(res.msg, { time: 1000 }, function () {
                if (res.success) {
                    location.reload();
                }
            });
       })
   }
   function toggleEmailVisibility() {
    const emailDiv = document.getElementById('email');
    const icon = document.querySelector('.toggle-visibility');
    
    if (emailDiv.getAttribute('data-hidden') === 'true') {
        // emailDiv.style.webkitTextSecurity = 'none';
        $('#email').attr('data-hidden', 'false'); 
        icon.classList.remove('icon-denglu-mimabukejian');
        icon.classList.add('icon-denglu-mimakejian');
        document.getElementById('email').innerText = '@Model.Mail'
    } else {
                 if('@Model.Mail'){
                document.getElementById('email').innerText = hideMiddleText('@Model.Mail')
        }
        // emailDiv.style.webkitTextSecurity = 'disc';
        $('#email').attr('data-hidden', 'true'); 

        icon.classList.remove('icon-denglu-mimakejian');
        icon.classList.add('icon-denglu-mimabukejian');
    }
}

// 页面加载时默认隐藏邮箱
document.addEventListener('DOMContentLoaded', function() {
    // const emailDiv = document.getElementById('email');
    if('@Model.Mail'){
            document.getElementById('email').innerText = hideMiddleText('@Model.Mail')
    }

    // emailDiv.style.webkitTextSecurity = 'disc';
});
</script>
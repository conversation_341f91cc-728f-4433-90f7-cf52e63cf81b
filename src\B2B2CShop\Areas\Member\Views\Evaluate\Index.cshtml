@using B2B2CShop.Dto
@using B2B2CShop.Entity
@using Pek.Timing
@inject IWorkContext workContext
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/comments.css");

    var site = SiteInfo.GetDefaultSeo();
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<style>
    .layui-laypage a,
    .layui-laypage span {
        font-size: 14px;
    }
    .layui-rate-disabled {
        pointer-events: none; /* 禁止鼠标事件 */
        cursor: not-allowed; /* 鼠标指针显示为禁用状态 */
    }
</style>
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Evaluate")">@T("评论")</a>
    </div>
    <!-- 用户中心 -->
    <div class="userInfoBox">
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
@*             <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
@*             <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content">
            <form action="@Url.Action("Index")">
                <div class="filterBox">
                    <div style="width: 40%;margin: 0;">
                        <div class="layui-form"><input class="layui-input" name="key" placeholder="@T("请输入订单号")/@T("BOM标识进行查询")" value="@Model.key"></div>
                    </div>
                    <button class="button button_blue" type="submit">@T("查询")</button>
                </div>
            </form>

            <div class="optionsBox">
                <div class="titleBox1">
                    <div data-select="false" onclick="titleClick(this,1)" class="titleSelect">@T("待评价订单")</div>
                    <div data-select="false" onclick="titleClick(this,2)">@T("已评价订单")</div>
                </div>
                <!-- 全选 -->
                <div class="titleBox2">
                  @*   <div class="layui-form">
                        <input type="checkbox" name="selectAll" id="selectAll">@T("全选")
                    </div>
                    <div> <button class="button button_red">@T("批量删除订单")</button> </div> *@
                </div>
            </div>

            <!-- 所有订单-表格 -->
            <div class="tablesBox">
                <table class="layui-table" style="background-color: white;">
                    <colgroup>
                        <col width="50%">
                        <col width="15%">
                        <col width="15%">
                    </colgroup>

                    @foreach (OrderDto item in Model.list)
                    {
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>
                                    <span class="layui-form" style="margin-right: .5vw;">
                                       @*  <input type="checkbox" name="order" value="@item.Id"> *@
                                    </span>
                                    <span style="margin-right: 5%">
                                        @item.AddTime
                                    </span>
                                    @T("订单编号"):
                                    <span id="orderId"> @item.OrderSn</span>
                                    <span class="textSelect pointer" id="copy" onclick="copy('#orderId')">
                                        @T("复制")
                                    </span>
                                </th>
                                <th class="tableIcon" style="margin-right: auto;">
                                    @* <i class="iconfont  icon-zaixiankefu1"></i> *@
                                </th>
                                <th>

                                </th>
                                <th class="tableIcon" style="text-align: right;">
                                    @*<span class="textSelect pointer" id="copy" onclick="toRouter(this)" data-link="@Url.Action("Add")?orderId=@item.Id">
                                        @T("一起评")
                                    </span> *@
                                   @*  <i class="iconfont  icon-icdelete" style="font-size: 1.2vw;"></i> *@
                                </th>
                            </tr>
                            <!--   -->
                        </thead>
                        <tbody>
                            @foreach (OrderGoodsDto goods in item.OrderGoods??new List<OrderGoodsDto>())
                            {
                                <tr>
                                    <td>
                                        <div class="goodsInfo" style="place-items: center;">
                                            <div class="layui-form" style="margin-right: .5vw;">
                                                @*  <input type="checkbox" name="BBB" checked> *@
                                            </div>
                                            <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                                <img src="@goods.GoodsImage" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                            </div>
                                            <div class="goodsInformation"
                                            style="width: 60%;margin-left: 5%;margin-right: auto;">
                                                <div class="tdTitle"> <span>@goods.GoodsName</span> </div>
                                                @*  <div>原厂编号<span class="name textOver">ACDC电源模块</span></div> *@
                                                <div>@T("制造商"): <span class="name textOver">@goods.CompanyName</span> </div>
                                                @*   <div class="textOver">
                                                    制造商编号: <span class="name textOver">727-S40FC008C3B1V000 </span>
                                                </div> *@
                                                <div class="textOver">
                                                    @T("型号"):<span class="name textOver">@goods.SpecValue</span>
                                                </div>
                                                @*                                                 <div class="textOver">
                                                    客户编号:<span class="name">220V转5V3.3V9V12V15V24V</span>
                                                </div> *@
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>@T("商品数量"): *@goods.GoodsNum  </div>
                                        <div>@T("商品单价")：@goods.SymbolLeft @goods.GoodsPayPrice </div>
                                    </td>
                                    @if(goods == item.OrderGoods?[0]){
                                        <td rowspan="@item.OrderGoods.Count" colspan="1" class="fnTd">
                                            <div class="hover">@T("交易成功")</div>
                                            <a class="hover" href="@Url.Action("OrderDetails", "Orders")?Id=@item.Id">@T("订单详情")</a>
                                            <div>@T("运费"): @item.SymbolLeft @item.ShippingFee</div>
                                            <div>@T("合计")：<span class="money">@item.SymbolLeft @item.OrderAmount</span> </div>
                                            @*   <div class="redmi">查看物流</div> *@
                                        </td>
                                        <td class="tableBtnBox" colspan="1" rowspan="@item.OrderGoods.Count">
                                            <div>
                                                <button class="button textSelect textOver" onclick="toRouter(this)" data-link="@Url.Action("Add")?orderId=@item.Id">@T("立即评价")</button>
                                            </div>
                                            <div class="flex">
                                                <button class="button bgSelect" onclick="toRouter(this)" data-link="@Url.Action("Index","Goods",new {Area = "",skuId = goods.SkuId})">@T("再次购买")</button>
                                            </div>
                                        </td>
                                    }
                                </tr>
                            }
                            @*                         <tr>
                            <td class="hover" style="text-align: center;" colspan="4">
                                --- 展开所有订单 ---
                            </td>
                        </tr> *@
                        </tbody>
                    }
                </table>
                <div id="pagingBox" style="text-align: right;"></div>
                <script>
                    function select(dom) {
                    const type = dom.getAttribute("data-type");
                    const parentDom = dom.parentNode;
                    $(parentDom).children().attr('class', '')
                    // console.log(parentDom,$(parentDom));
                    if (type == 0) {
                    dom.className = "bgSelect";
                    } else if (type == 1) {
                    dom.className = "bgSelect";
                    } else if (type == 2) {
                    dom.className = "bgSelect";
                    } else if (type == 3) {
                    dom.className = "bgSelect";
                    }
                    }
                    layui.use(function () {
                    var laypage = layui.laypage;
                    laypage.render({
                    elem: 'pagingBox',
                    count: '@Model.total', // 数据总数
                    limit: '@ViewBag.limit',
                    limits:[10,20,30,50],
                    prev:'@T("上一页")',
                    next:'@T("下一页")',
                    theme: '#2C79E8'
                    });
                    });
                </script>

            </div>

            <!-- 所有订单-表格 -->
            <div class="tablesBox2" style="display: none;">
                <table class="layui-table" style="background-color: white;">
                    <colgroup>
                        <col width="50%">
                        <col width="45%">
                    </colgroup>
                    @foreach (OrderDto item in Model.list2)
                    {
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>
                                    <span class="layui-form" style="margin-right: .5vw;">
                                       @*  <input type="checkbox" name="BBB" checked> *@
                                    </span>
                                    <span style="margin-right: 5%">
                                        @item.AddTime
                                    </span>
                                    @T("订单编号"):
                                    <span id="orderId"> @item.OrderSn</span>
                                    <span class="textSelect pointer" id="copy" onclick="copy('#orderId')">
                                        @T("复制")
                                    </span>
                                </th>
                                <th class="tableIcon" style="text-align: right;">
                                    @* <i class="iconfont  icon-icdelete" style="font-size: 1.2vw;"></i> *@
                                </th>
                            </tr>
                            <!--   -->
                        </thead>
                        <tbody>
                            @foreach (OrderGoodsDto goods in item.OrderGoods?? new List<OrderGoodsDto>())
                            {
                                <tr>
                                    <td>
                                        <div class="goodsInfo" style="place-items: center;">
                                            <div class="layui-form" style="margin-right: .5vw;">
                                                @* <input type="checkbox" name="BBB" checked> *@
                                            </div>
                                            <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                                <img src="@goods.GoodsImage" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                            </div>
                                            <div class="goodsInformation"
                                            style="width: 60%;margin-left: 5%;margin-right: auto;">
                                                <div class="tdTitle"> <span>@goods.GoodsName</span> </div>
                                                @* <div>原厂编号<span class="name textOver">ACDC电源模块</span></div> *@
                                                <div>@T("制造商"): <span class="name textOver">@goods.CompanyName</span> </div>
                                                @*                                                 <div class="textOver">
                                                    制造商编号: <span class="name textOver">727-S40FC008C3B1V000 </span>
                                                </div> *@
                                                <div class="textOver">
                                                    @T("型号"):<span class="name textOver">@goods.SpecValue</span>
                                                </div>
                                                @*                                                 <div class="textOver">
                                                    客户编号:<span class="name">220V转5V3.3V9V12V15V24V</span>
                                                </div> *@
                                            </div>
                                        </div>
                                    </td>
                                    @if (goods == item.OrderGoods?[0])
                                    {
                                        <td rowspan="@item.OrderGoods.Count" colspan="1">
                                            <div id="@item.Id"></div>
                                            <script>
                                                layui.use(function(){
                                                    var rate = layui.rate;
                                                    // 渲染

                                                    var score = '@item.EvaluateInfo?.GoodsScore';
                                                    var num = score / 2;

                                                    rate.render({
                                                        elem: '#@item.Id',
                                                        value: num, // 初始值
                                                        readonly:true,
                                                        half: true // 开启半星
                                                    });
                                                });
                                            </script>
                                            <div class="commentPics">
                                                <img src="@item.EvaluateInfo?.Image1" alt="">
                                                <img src="@item.EvaluateInfo?.Image2" alt="">
                                                <img src="@item.EvaluateInfo?.Image3" alt="">
                                                <img src="@item.EvaluateInfo?.Image4" alt="">
                                                <img src="@item.EvaluateInfo?.Image5" alt="">
                                                <img src="@item.EvaluateInfo?.Image6" alt="">   
                                            </div>
                                            <div class="comment">
                                                @item.EvaluateInfo?.Content
                                            </div>
                                            @{
                                                var time = UnixTime.ToDateTime(item.EvaluateInfo?.AddTime??0);
                                            }
                                            <div class="date">
                                                @time
                                            </div>
                                        </td>
                                    }
                                </tr>
                            }



                            @*                         <tr>
                            <td class="hover" style="text-align: center;" colspan="4">
                                --- 展开所有订单 ---
                            </td>
                        </tr> *@
                        </tbody>
                    }
                </table>
                <div id="pagingBox2" style="text-align: right;"></div>
                <script>
                    function select(dom) {
                        const type = dom.getAttribute("data-type");
                        const parentDom = dom.parentNode;
                        $(parentDom).children().attr('class', '')
                        // console.log(parentDom,$(parentDom));
                        if (type == 0) {
                            dom.className = "bgSelect";
                        } else if (type == 1) {
                            dom.className = "bgSelect";
                        } else if (type == 2) {
                            dom.className = "bgSelect";
                        } else if (type == 3) {
                            dom.className = "bgSelect";
                        }
                    }
                    layui.use(function () {
                        var laypage = layui.laypage;
                        laypage.render({
                            elem: 'pagingBox2',
                            count: '@Model.total', // 数据总数
                            prev:'@T("上一页")',
                            next:'@T("下一页")',
                            theme: '#2C79E8'
                        });
                    });
                </script>

            </div>
                
                
            </div>
    </div>
    <!-- 产品推荐 -国内才有 外国注重隐私 -->
    <div class="productComment">
        <div class="title">
            @T("相似推荐")
        </div>
        <div class="mainBox2_container">
            @foreach (GoodsDto item in ViewBag.Randomlist)
            {
                <div class="mainBox2_content">
                    <div>
                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                            <img src="@item.GoodsImage" alt="@T("商品图片")">
                        </a>
                    </div>
                    <div class="mainBox2_content_desc">@item.Name</div>
                    <div class="gray">
                        @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                    </div>
                    <div class="mainBox2_content_price">@<EMAIL></div>
                    <!-- 加入购物车 -->
                    @if (item.GoodsStorage <= 0)
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','@T("库存不足")',false)"></div>
                    }
                    else
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','',true)"></div>
                    }
                </div>
            }
        </div>
    </div>

</div>
<script src="/public/script/lazyload.js"></script>
<script>
window.addEventListener('load', function() {
    window.lazyLoadImages();
});
    function titleClick(dom, index) {
        $(dom).siblings().removeClass('titleSelect');
        $(dom).addClass('titleSelect');
        // 以下为显示隐藏上面两个title的对应内容
        if (index == 1) {
            $('.tablesBox').nextUntil('.productComment').hide();
            $('.titleBox2').show();
            if ($('.tablesBox')[0].style.display === 'none') {
                $('.optionsBox').nextUntil('.tablesBox2').show();
            }
        } else {

            $('.optionsBox').nextUntil('.tablesBox2').hide();
            //$('.titleBox2').hide();
            // console.log(dom,index,$('.tablesBox2')[0]);
            if ($('.tablesBox2')[0].style.display === 'none') {
                $('.tablesBox').nextUntil('.productComment').show();
            }
        }
    }
</script>
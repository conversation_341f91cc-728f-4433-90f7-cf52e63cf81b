@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IManageProvider _provider
@inject IWebHelper _webHelper
@inject IWorkContext workContext
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/accountInfo.css");

    var user = _provider.TryLogin(Context);
    var returnUrl = _webHelper.GetRawUrl(Context.Request);
    var site = SiteInfo.GetDefaultSeo();

    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Account")">@T("账号信息")</a>
    </div>
    <!-- <iframe src="../iframes/success.html" frameborder="0"  class="mask" id="mask"></iframe> -->

    <!-- 用户中心 -->
    <div class="userInfoBox">
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            @* <a href="@Url.Action("Index", "ShoppingCart", new { area = "Member" })"> *@
            @*     <div class="iconfont icon-a-cartline"></div> *@
            @*     <div>@T("购物车")</div> *@
            @* </a> *@
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
@*             <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
@*             <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content">
            <div class="box1">
                <div class="" style="width: 80px;height: 80px;margin: 10px 10px 0 10px;">
                    <img src="~/public/images/icons/morentouxiang.png">
                </div>
                <div class="">
                    <div class="flex">
                        <div class="textSelect">
                            @* @if (ValidateHelper.IsMobile(user.Name))
                            {
                                 @DesensitizedUtil.Mobile(user.Name);

                            }
                            else if (ValidateHelper.IsEmail(user.Name))
                            {
                                @DesensitizedUtil.Email(user.Name);

                            }
                            else
                            {
                                @DesensitizedUtil.ReplaceWithSpecialChar(user.Name);

                            } *@
                        </div>
                        <div style="margin-left: 10px;">@Model.Name @T("您好")！</div>
                    </div>
                </div>
                <div class="flex fnBox">
                    <div>
                        <a href="@Url.Action("Index","Wish")">
                            <div><img src="~/public/images/icons/xinyuanqingdan.png" alt=""></div>
                            <div>@T("心愿清单")</div>
                        </a>
                    </div>
                    <div>
                        <a href="@Url.Action("Index","GoodsBrowse")">
                            <div><img src="~/public/images/icons/lishi.png" alt=""></div>
                            <div>@T("历史记录")</div>
                        </a>
                    </div>
                    @* <div onclick="toRouter(this)">
                        <div><img src="~/public/images/icons/weiduxinxi.png"></div>
                        <!-- <div class="iconfont icon-notification-filled"></div> -->
                        <div>@T("未读信息")</div>
                        <div class="notices"> 99+ </div> 
                    </div> *@
                </div>
            </div>


            <div class="box1">
                <div class="title">
                    @T("我的订单")
                </div>
                <div class="flex fnBox" style="width: 100%;">
                    <div style="cursor: pointer;">
                        <a href="@Url.Action("Index","Orders",new{orderState = 10})">
                            <div class="iconfont iconSize30 icon-creditcard"></div>
                            <div>@T("待支付")<span class="textSelect">@ViewBag.OrderStateNum.State1</span></div>
                        </a>
                    </div>
                    <div style="cursor: pointer;">
                        <a href="@Url.Action("Index","Orders",new{orderState = 20})">
                            <div class="iconfont iconSize30 icon-wuliuxinxi"></div>
                            <div>@T("未发货") <span class="textSelect">@ViewBag.OrderStateNum.State2</span></div>
                        </a>
                    </div>
                    <div style="cursor: pointer;">
                        <a href="@Url.Action("Index","Orders",new{orderState = 30})">
                            <div class="iconfont iconSize30 icon-wuliu"></div>
                            <div>@T("配送中") <span class="textSelect">@ViewBag.OrderStateNum.State3</span></div>
                        </a>
                        @*  <div class="notices"> 99+ </div> *@
                    </div>
                    <div style="cursor: pointer;">
                        <a href="@Url.Action("Index","Orders",new{orderState = 40})">
                            <div class="iconfont iconSize30 icon-yishouhuo1"></div>
                            <div>@T("已签收") <span class="textSelect">@ViewBag.OrderStateNum.State5</span></div>
                        </a>
                        @* <div class="notices"> 99+ </div> *@
                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- 产品推荐 -国内才有 外国注重隐私 -->
    <div class="productComment">
        <div class="title">
            @T("相似推荐")
        </div>
        <div class="mainBox2_container">
            @foreach (GoodsDto item in ViewBag.Randomlist)
            {
                <div class="mainBox2_content">
                    <div>
                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                            <img src="@item.GoodsImage" alt="@T("商品图片")">
                        </a>
                    </div>
                    <div class="mainBox2_content_desc">@item.Name</div>
                    <div class="gray">
                        @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                    </div>
                    <div class="mainBox2_content_price">@<EMAIL></div>
                    <!-- 加入购物车 -->
                    @if (item.GoodsStorage <= 0)
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','@T("库存不足")',false)"></div>
                    }
                    else
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','',true)"></div>
                    }
                </div>
            }
        </div>
    </div>

</div>